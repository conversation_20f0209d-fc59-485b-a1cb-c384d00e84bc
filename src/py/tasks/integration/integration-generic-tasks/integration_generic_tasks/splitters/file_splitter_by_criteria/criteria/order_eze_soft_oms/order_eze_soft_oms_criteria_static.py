from aries_se_core_tasks.utilities.data_utils import BaseColumns
from enum import auto
from se_enums.core import BaseStrEnum

UNIQUE_IDENTIFIER_REGEX = "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])"


# When processing EZE Soft OMS Orders, there is a concept of different file types.
# The FileSplitterByCriteria for EZE Soft OMS will process files containing
# Executions, Allocations, and Orders data.
# It can also create consolidated files that merge data across file types.
class EzeSoftOMSControllerOutput:
    EXECUTIONS = "executions"
    ALLOCATIONS = "allocations"
    ORDERS = "orders"
    EXECUTIONS_ALLOCATIONS_CONSOLIDATED = "executions_allocations_consolidated"


class EzeSoftOMSFileTypeEnum(BaseStrEnum):
    EXECUTIONS = auto()
    ALLOCATIONS = auto()
    ORDERS = auto()


class ExecutionsSourceColumns(BaseColumns):
    EXECUTION_ID = "execution_id"
    ORDER_ID = "order_id"
    EXECUTION_TIME = "execution_time"
    QUANTITY = "quantity"
    PRICE = "price"


class AllocationsSourceColumns(BaseColumns):
    ALLOCATION_ID = "allocation_id"
    ORDER_ID = "order_id"
    ALLOCATION_TIME = "allocation_time"
    QUANTITY = "quantity"
    ACCOUNT = "account"


class OrdersSourceColumns(BaseColumns):
    ORDER_ID = "order_id"
    ORDER_TIME = "order_time"
    SYMBOL = "symbol"
    SIDE = "side"
    QUANTITY = "quantity"
    PRICE = "price"
    ORDER_TYPE = "order_type"


# File type prefixes for column naming when merging DataFrames
class FileTypes:
    EXECUTIONS = "executions"
    ALLOCATIONS = "allocations"
    ORDERS = "orders"


def add_prefix(file_type: str, column_name: str) -> str:
    """Add file type prefix to column name for DataFrame merging.

    :param file_type: The file type (executions, allocations, orders)
    :param column_name: The original column name
    :return: Prefixed column name
    """
    return f"{file_type}_{column_name}"


# Development columns for internal processing
class DevColumns:
    ORDER_MERGE_ID = "order_merge_id"
    EXECUTION_MERGE_ID = "execution_merge_id"
    ALLOCATION_MERGE_ID = "allocation_merge_id"

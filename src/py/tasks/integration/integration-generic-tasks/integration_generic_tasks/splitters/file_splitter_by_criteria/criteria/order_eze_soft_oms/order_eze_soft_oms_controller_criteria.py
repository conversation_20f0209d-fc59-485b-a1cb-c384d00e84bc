# ruff: noqa: E501
import fsspec
import json
import logging
import pandas as pd
import re
from addict import addict
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    Params as MultipleFilesInputControllerParams,
)
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    run_multiple_files_input_controller,
)
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.frame.merge_and_split_dataframes import (
    Params as MergeAndSplitDataFramesParams,
)
from aries_se_core_tasks.frame.merge_and_split_dataframes import (
    run_merge_and_split_dataframes,
)
from aries_se_core_tasks.io.read.cloud.download_file import (
    run_download_file,
)
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_task_link.models import AriesTaskInput
from collections import defaultdict
from datetime import datetime
from integration_audit.auditor import AuditorStaticFields, upsert_audit
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.abstract_order_eze_soft_oms_controller_criteria import (
    AbstractOrderEzeSoftOMSControllerCriteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.order_eze_soft_oms_criteria_exceptions import (
    SkipIfFileIsNotPartOfTheWorkflow,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.order_eze_soft_oms_criteria_static import (
    UNIQUE_IDENTIFIER_REGEX,
    EzeSoftOMSControllerOutput,
    EzeSoftOMSFileTypeEnum,
    ExecutionsSourceColumns,
    AllocationsSourceColumns,
    OrdersSourceColumns,
    FileTypes,
    add_prefix,
    DevColumns,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfMissingFiles,
    SkipIfSourceTimestampLessThanPair,
    SkipIfSourceTimestampSameAlphaLess,
)
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.core.exception import TaskException as CoreTaskException
from se_core_tasks.io.read.csv_file_splitter import (
    Params as ParamsCsvFileSplitter,
)
from se_core_tasks.io.read.csv_file_splitter import PreProcessFunc
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import (
    get_ingest_lake_dir_for_task,
    get_prefixed_ingest_lake_path_for_task,
)
from se_enums.cloud import CloudProviderEnum
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Dict, List, Tuple

logger_ = logging.getLogger(__name__)


class DefaultOrderEzeSoftOMSControllerCriteria(AbstractOrderEzeSoftOMSControllerCriteria):
    @property
    def _should_process_executions_separately(self) -> bool:
        return False

    @property
    def _batch_size_override(self) -> int | None:
        return None

    def criteria_driver(
        self,
        aries_task_input: AriesTaskInput,
        file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
        workflow_config: addict.Dict,
        audit_path: str,
        **kwargs,
    ) -> Dict[str, Any]:
        """EZE Soft OMS controller criteria driver.

        This workflow processes files containing Executions, Allocations, and Orders data.
        The workflow is triggered by any of these file types and will check for the presence
        of all required files before proceeding.

        :param aries_task_input: Task input AriesTaskInput object propagated through Conductor
        :param file_splitter_by_criteria_aries_task_input: The params of `aries_task_input` converted to a standardized Pydantic object
        :param workflow_config: addict.Dict containing the order_eze_soft_oms workflow configuration fetched from the config DB.
        :param audit_path: The path to the JSON audit file, whose contents may be updated throughout this module.

        :return: A dictionary with keys for each file type containing lists of DynamicTask objects
        that will be executed downstream in the workflow.
        """

        cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
            file_uri=workflow_config.tenant.lake_prefix
        )
        realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)
        streamed = workflow_config.workflow.streamed

        # Create temporary directory
        temp_dir: Path = tmp_directory()

        try:
            file_paths_by_type, target_cache_path = self.extract_and_process_files(
                cloud_provider=cloud_provider,
                file_url=file_splitter_by_criteria_aries_task_input.file_uri,
                realm=realm,
                batch_size=self._batch_size_override or workflow_config.max_batch_size,
                temp_dir=temp_dir,
                streamed=streamed,
                audit_path=audit_path,
                aries_task_input=aries_task_input,
            )

        except (
            # to prevent the workflow from running multiple times for the same set of files
            SkipIfSourceTimestampLessThanPair,
            SkipIfSourceTimestampSameAlphaLess,
            SkipIfMissingFiles,  # the workflow cannot yet start
        ) as skip_exception:
            logger_.warning(f"Skipping the workflow: {skip_exception.message}")

            # If any of the above exceptions are raised, the workflow cannot proceed;
            # thus, we return a result with empty lists, to force the
            # downstream Dynamic Fork Joins to do nothing.
            # We also update the audit file explaining why the workflow was skipped.
            return self.create_empty_output(
                aries_task_input=aries_task_input,
                audit_path=audit_path,
                file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
                skip_exception=skip_exception,
                streamed=streamed,
            )

        except (
            SkipIfFileIsNotPartOfTheWorkflow,  # the input file is irrelevant for the workflow
        ) as skip_exception:
            logger_.warning(f"Skipping the workflow: {skip_exception.message}")

            return self.create_empty_output(
                aries_task_input=aries_task_input,
                audit_path=audit_path,
                file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
                skip_exception=skip_exception,
                streamed=streamed,
            )

        # Create dynamic tasks for each file type
        output = {}
        for file_type, file_paths in file_paths_by_type.items():
            if file_type in file_splitter_by_criteria_aries_task_input.dynamic_tasks:
                dynamic_task_input: DynamicTask = (
                    file_splitter_by_criteria_aries_task_input.dynamic_tasks[file_type]
                )

                output[file_type] = create_dynamic_tasks_list(
                    lst=[{"file_uri": file_path} for file_path in file_paths],
                    task=dynamic_task_input,
                    common_input_parameters={
                        "source_file_uri": file_splitter_by_criteria_aries_task_input.file_uri,
                        "file_type": file_type,
                        "cache_path": target_cache_path,
                    },
                    workflow=aries_task_input.workflow,
                )
            else:
                # If no dynamic task is defined for this file type, create empty list
                output[file_type] = []

        return output

    @staticmethod
    def create_empty_output(
        aries_task_input: AriesTaskInput,
        audit_path: str,
        file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
        skip_exception: TaskException | CoreTaskException,
        streamed: bool,
    ) -> Dict[str, Any]:
        """Creates an empty output structure and updates the audit message.

        :param aries_task_input: Input object encapsulating workflow-related data necessary
            for creating dynamic tasks, including common input parameters.
        :param audit_path: The location of the task audit file.
        :param file_splitter_by_criteria_aries_task_input: An object containing the dynamic
            tasks and related parameters for segregating tasks based on specific criteria.
        :param skip_exception: Exception that was raised by MultipleFilesInputController.
        :param streamed: Indicator for whether the workflow is streamed or non-streamed.

        :return: A dictionary containing empty task lists for all file types.
        """
        exception_message: str = skip_exception.message
        upsert_audit(
            audit_path=audit_path,
            streamed=streamed,
            workflow_status=[exception_message],
        )

        output = {}
        for file_type in [
            EzeSoftOMSControllerOutput.EXECUTIONS,
            EzeSoftOMSControllerOutput.ALLOCATIONS,
            EzeSoftOMSControllerOutput.ORDERS,
            EzeSoftOMSControllerOutput.EXECUTIONS_ALLOCATIONS_CONSOLIDATED
        ]:
            if file_type in file_splitter_by_criteria_aries_task_input.dynamic_tasks:
                output[file_type] = create_dynamic_tasks_list(
                    lst=[],
                    task=file_splitter_by_criteria_aries_task_input.dynamic_tasks[file_type],
                    workflow=aries_task_input.workflow,
                    common_input_parameters={},
                )
            else:
                output[file_type] = []

        return output

    def extract_and_process_files(
        self,
        file_url: str,
        realm: str,
        batch_size: int,
        temp_dir: Path,
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        audit_path: str,
        aries_task_input: AriesTaskInput,
    ) -> Tuple[Dict[str, List[str]], str]:
        """This task is responsible for evaluating if the workflow meets all
        requirements and can proceed. If it can, it will iterate over the input
        files and process them into batches by file type.

        :param file_url: Absolute file path of the file that triggered the workflow
        :param realm: Tenant bucket without the cloud provider prefix
        :param batch_size: Max batch size as defined in the config DB
        :param temp_dir: Temporary directory where the batches are stored in disk
        :param streamed: True or false, as defined in the config DB.
        :param cloud_provider: Cloud provider as defined in the config DB.
        :param audit_path: Absolute path of the JSON audit file
        :param aries_task_input: Task input AriesTaskInput object propagated through Conductor

        :return: A tuple with exactly two elements:
        - A dictionary mapping file types to lists of cloud URIs for processing downstream
        - The cloud URI of the cache file that must be propagated downstream
        """

        logger_.info(f"Extracting and processing files for: {file_url}")

        file_paths_by_type: Dict[str, List[str]] = {
            EzeSoftOMSControllerOutput.EXECUTIONS: [],
            EzeSoftOMSControllerOutput.ALLOCATIONS: [],
            EzeSoftOMSControllerOutput.ORDERS: [],
            EzeSoftOMSControllerOutput.EXECUTIONS_ALLOCATIONS_CONSOLIDATED: [],
        }
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        output_file_name = re.sub(
            r"\.(Executions|Allocations|Orders)\.",
            ".",
            Path(file_url).name,
        ).replace(".csv", "")

        # Create the appropriate path where the files are to be uploaded
        output_lake_path = Path(
            get_ingest_lake_dir_for_task(
                workflow_name=aries_task_input.workflow.name,
                task_name="file_splitter_by_criteria",
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
            )
        )

        multiple_files_input_controller_df: pd.DataFrame = self.collect_required_file_uris(
            cloud_provider, file_url, realm
        )

        logger_.info("Input file requirements were met. Starting to process the input files.")

        # Process files by type and create batches
        batches_by_file_type: Dict[str, List[FileSplitterResult]] = self.batch_input_files(
            multiple_files_input_controller_result=multiple_files_input_controller_df,
            batch_size=batch_size,
            realm=realm,
            streamed=streamed,
            audit_path=audit_path,
            temp_dir=temp_dir,
        )

        # Load all Orders data first for merging
        orders_df = self.load_complete_dataframe_by_type(
            batches_by_file_type.get(EzeSoftOMSControllerOutput.ORDERS, []),
            EzeSoftOMSControllerOutput.ORDERS
        )

        # Process Executions with Orders enrichment
        if EzeSoftOMSControllerOutput.EXECUTIONS in batches_by_file_type:
            self.process_and_upload_enriched_batches(
                file_type=EzeSoftOMSControllerOutput.EXECUTIONS,
                batches=batches_by_file_type[EzeSoftOMSControllerOutput.EXECUTIONS],
                orders_df=orders_df,
                file_paths_by_type=file_paths_by_type,
                cloud_provider_prefix=cloud_provider_prefix,
                realm=realm,
                output_file_name=output_file_name,
                output_lake_path=output_lake_path,
                batch_size=batch_size,
            )

        # Process Allocations with Orders enrichment
        if EzeSoftOMSControllerOutput.ALLOCATIONS in batches_by_file_type:
            self.process_and_upload_enriched_batches(
                file_type=EzeSoftOMSControllerOutput.ALLOCATIONS,
                batches=batches_by_file_type[EzeSoftOMSControllerOutput.ALLOCATIONS],
                orders_df=orders_df,
                file_paths_by_type=file_paths_by_type,
                cloud_provider_prefix=cloud_provider_prefix,
                realm=realm,
                output_file_name=output_file_name,
                output_lake_path=output_lake_path,
                batch_size=batch_size,
            )

        # Process Orders (standalone, no enrichment needed)
        if EzeSoftOMSControllerOutput.ORDERS in batches_by_file_type:
            self.process_and_upload_standalone_batches(
                file_type=EzeSoftOMSControllerOutput.ORDERS,
                batches=batches_by_file_type[EzeSoftOMSControllerOutput.ORDERS],
                file_paths_by_type=file_paths_by_type,
                cloud_provider_prefix=cloud_provider_prefix,
                realm=realm,
                output_file_name=output_file_name,
                output_lake_path=output_lake_path,
                batch_size=batch_size,
            )

        # Create consolidated Executions-Allocations files if both file types are present
        if (EzeSoftOMSControllerOutput.EXECUTIONS in batches_by_file_type and
            EzeSoftOMSControllerOutput.ALLOCATIONS in batches_by_file_type):
            self.create_consolidated_executions_allocations_files(
                executions_batches=batches_by_file_type[EzeSoftOMSControllerOutput.EXECUTIONS],
                allocations_batches=batches_by_file_type[EzeSoftOMSControllerOutput.ALLOCATIONS],
                orders_df=orders_df,
                file_paths_by_type=file_paths_by_type,
                cloud_provider_prefix=cloud_provider_prefix,
                realm=realm,
                output_file_name=output_file_name,
                output_lake_path=output_lake_path,
                batch_size=batch_size,
            )

        # Create and upload cache file
        cache_data = {
            "processed_files": len(multiple_files_input_controller_df),
            "processing_timestamp": datetime.utcnow().isoformat(),
            "file_types_processed": list(file_paths_by_type.keys()),
        }

        target_cache_path = (
            cloud_provider_prefix
            + realm
            + "/"
            + get_prefixed_ingest_lake_path_for_task(
                workflow_name=aries_task_input.workflow.name,
                workflow_trace_id=aries_task_input.workflow.trace_id,
                task_name=aries_task_input.task.name,
                task_io_params=aries_task_input.input_param.params,
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            )
            + "processing_cache.json"
        )

        logger_.info(f"Uploading cache to {target_cache_path}")

        with fsspec.open(target_cache_path, "w") as f:
            f.write(json.dumps(cache_data))

        logger_.info("Cache uploaded successfully.")

        return file_paths_by_type, target_cache_path

    # def load_complete_dataframe_by_type(
    #     self,
    #     batches: List[FileSplitterResult],
    #     file_type: str,
    # ) -> pd.DataFrame:
    #     """Load all batches of a specific file type into a single DataFrame.
    #
    #     :param batches: List of FileSplitterResult objects for the file type
    #     :param file_type: The type of file being loaded (for logging)
    #
    #     :return: Combined DataFrame containing all data from the batches
    #     """
    #     logger_.info(f"Loading complete {file_type} DataFrame for merging")
    #
    #     combined_df = pd.DataFrame()
    #
    #     for batch in batches:
    #         if batch.input_total_count == 0:
    #             continue
    #
    #         batch_file_path = batch.path.as_posix()
    #         batch_df = pd.read_csv(batch_file_path, encoding=batch.encoding)
    #
    #         combined_df = pd.concat([combined_df, batch_df], axis=0, ignore_index=True)
    #         del batch_df
    #
    #     logger_.info(f"Loaded {len(combined_df)} records for {file_type}")
    #     return combined_df
    #
    # def process_and_upload_enriched_batches(
    #     self,
    #     file_type: str,
    #     batches: List[FileSplitterResult],
    #     orders_df: pd.DataFrame,
    #     file_paths_by_type: Dict[str, List[str]],
    #     cloud_provider_prefix: str,
    #     realm: str,
    #     output_file_name: str,
    #     output_lake_path: Path,
    #     batch_size: int,
    # ) -> None:
    #     """Process batches of Executions or Allocations and enrich them with Orders data.
    #
    #     :param file_type: Type of file being processed (EXECUTIONS or ALLOCATIONS)
    #     :param batches: List of FileSplitterResult objects to process
    #     :param orders_df: Complete Orders DataFrame for merging
    #     :param file_paths_by_type: Dictionary to store output file paths
    #     :param cloud_provider_prefix: Cloud provider prefix for file paths
    #     :param realm: Tenant bucket name
    #     :param output_file_name: Base name for output files
    #     :param output_lake_path: Path where output files will be stored
    #     :param batch_size: Maximum batch size for output files
    #     """
    #     logger_.info(f"Processing and enriching {file_type} batches with Orders data")
    #
    #     for batch in batches:
    #         if batch.input_total_count == 0:
    #             continue
    #
    #         batch_file_path = batch.path.as_posix()
    #         batch_df = pd.read_csv(batch_file_path, encoding=batch.encoding)
    #
    #         # Convert integer columns to nullable integers
    #         for col in batch_df.select_dtypes(include="int").columns:
    #             batch_df[col] = batch_df[col].astype("Int64")
    #
    #         # Merge with Orders data based on ORDER_ID
    #         if not orders_df.empty:
    #             # Determine the correct ORDER_ID column based on file type
    #             left_order_id_col = (
    #                 ExecutionsSourceColumns.ORDER_ID
    #                 if file_type == EzeSoftOMSControllerOutput.EXECUTIONS
    #                 else AllocationsSourceColumns.ORDER_ID
    #             )
    #
    #             enriched_df = batch_df.merge(
    #                 orders_df,
    #                 how="left",
    #                 left_on=left_order_id_col,
    #                 right_on=OrdersSourceColumns.ORDER_ID,
    #                 suffixes=('', '_orders')
    #             )
    #
    #             logger_.info(f"Enriched {file_type} batch: {len(batch_df)} -> {len(enriched_df)} records")
    #
    #             # Log merge statistics
    #             matched_records = enriched_df[OrdersSourceColumns.ORDER_ID + '_orders'].notna().sum()
    #             logger_.info(f"Merge statistics: {matched_records}/{len(batch_df)} records matched with Orders data")
    #         else:
    #             enriched_df = batch_df
    #             logger_.warning(f"No Orders data available for enriching {file_type}")
    #
    #         # Generate output file path
    #         output_file_name_with_type = Path(
    #             f"{output_file_name}_{file_type}_enriched_batch_{batch.batch_index}.csv"
    #         )
    #         output_file_path = (
    #             cloud_provider_prefix
    #             + realm
    #             + "/"
    #             + Path.joinpath(output_lake_path, output_file_name_with_type).as_posix()
    #         )
    #
    #         # Handle large batches by splitting them further if needed
    #         self.upload_dataframe_with_batching(
    #             df=enriched_df,
    #             output_file_path=output_file_path,
    #             batch_size=batch_size,
    #             file_paths_by_type=file_paths_by_type,
    #             file_type=file_type,
    #         )
    #
    #         del batch_df, enriched_df

    def process_and_upload_standalone_batches(
        self,
        file_type: str,
        batches: List[FileSplitterResult],
        file_paths_by_type: Dict[str, List[str]],
        cloud_provider_prefix: str,
        realm: str,
        output_file_name: str,
        output_lake_path: Path,
        batch_size: int,
    ) -> None:
        """Process batches without enrichment (for Orders files).

        :param file_type: Type of file being processed
        :param batches: List of FileSplitterResult objects to process
        :param file_paths_by_type: Dictionary to store output file paths
        :param cloud_provider_prefix: Cloud provider prefix for file paths
        :param realm: Tenant bucket name
        :param output_file_name: Base name for output files
        :param output_lake_path: Path where output files will be stored
        :param batch_size: Maximum batch size for output files
        """
        logger_.info(f"Processing {file_type} batches (standalone)")

        for batch in batches:
            if batch.input_total_count == 0:
                continue

            batch_file_path = batch.path.as_posix()
            batch_df = pd.read_csv(batch_file_path, encoding=batch.encoding)

            # Convert integer columns to nullable integers
            for col in batch_df.select_dtypes(include="int").columns:
                batch_df[col] = batch_df[col].astype("Int64")

            # Generate output file path
            output_file_name_with_type = Path(
                f"{output_file_name}_{file_type}_batch_{batch.batch_index}.csv"
            )
            output_file_path = (
                cloud_provider_prefix
                + realm
                + "/"
                + Path.joinpath(output_lake_path, output_file_name_with_type).as_posix()
            )

            # Handle large batches by splitting them further if needed
            self.upload_dataframe_with_batching(
                df=batch_df,
                output_file_path=output_file_path,
                batch_size=batch_size,
                file_paths_by_type=file_paths_by_type,
                file_type=file_type,
            )

            del batch_df

    def upload_dataframe_with_batching(
        self,
        df: pd.DataFrame,
        output_file_path: str,
        batch_size: int,
        file_paths_by_type: Dict[str, List[str]],
        file_type: str,
    ) -> None:
        """Upload a DataFrame, splitting into sub-batches if necessary.

        :param df: DataFrame to upload
        :param output_file_path: Base output file path
        :param batch_size: Maximum batch size
        :param file_paths_by_type: Dictionary to store output file paths
        :param file_type: Type of file being uploaded
        """
        if df.shape[0] > batch_size:
            sub_batches_list: List[pd.DataFrame] = [
                sub_df.frame()
                for sub_df in run_merge_and_split_dataframes(
                    source_frame_list=[df],
                    params=MergeAndSplitDataFramesParams(max_chunk_size=batch_size),
                )
            ]

            for i, sub_batch in enumerate(sub_batches_list):
                sub_batch_output_file_path = output_file_path.replace(".csv", f"_{i}.csv")
                logger_.info(f"Writing sub-batch to: {sub_batch_output_file_path}")
                sub_batch.to_csv(sub_batch_output_file_path, index=False)
                file_paths_by_type[file_type].append(sub_batch_output_file_path)

            del sub_batches_list
        else:
            logger_.info(f"Writing batch to: {output_file_path}")
            df.to_csv(output_file_path, index=False)
            file_paths_by_type[file_type].append(output_file_path)

    def create_consolidated_executions_allocations_files(
        self,
        executions_batches: List[FileSplitterResult],
        allocations_batches: List[FileSplitterResult],
        orders_df: pd.DataFrame,
        file_paths_by_type: Dict[str, List[str]],
        cloud_provider_prefix: str,
        realm: str,
        output_file_name: str,
        output_lake_path: Path,
        batch_size: int,
    ) -> None:
        """Create consolidated files that merge Executions and Allocations through ORDER_ID.

        This creates a Many-to-Many relationship file where each execution can be related
        to multiple allocations through the same ORDER_ID.

        :param executions_batches: List of execution file batches
        :param allocations_batches: List of allocation file batches
        :param orders_df: Complete Orders DataFrame for enrichment
        :param file_paths_by_type: Dictionary to store output file paths
        :param cloud_provider_prefix: Cloud provider prefix for file paths
        :param realm: Tenant bucket name
        :param output_file_name: Base name for output files
        :param output_lake_path: Path where output files will be stored
        :param batch_size: Maximum batch size for output files
        """
        logger_.info("Creating consolidated Executions-Allocations files")

        # Load complete Executions and Allocations DataFrames
        executions_df = self.load_complete_dataframe_by_type(
            executions_batches, EzeSoftOMSControllerOutput.EXECUTIONS
        )
        allocations_df = self.load_complete_dataframe_by_type(
            allocations_batches, EzeSoftOMSControllerOutput.ALLOCATIONS
        )

        if executions_df.empty or allocations_df.empty:
            logger_.warning("Cannot create consolidated files: missing Executions or Allocations data")
            return

        # Merge Executions with Allocations through ORDER_ID (Many-to-Many)
        consolidated_df = executions_df.merge(
            allocations_df,
            how="inner",  # Only include records where both execution and allocation exist
            left_on=ExecutionsSourceColumns.ORDER_ID,
            right_on=AllocationsSourceColumns.ORDER_ID,
            suffixes=('_execution', '_allocation')
        )

        logger_.info(f"Consolidated Executions-Allocations: {len(executions_df)} executions × {len(allocations_df)} allocations = {len(consolidated_df)} records")

        # Enrich with Orders data if available
        if not orders_df.empty:
            consolidated_df = consolidated_df.merge(
                orders_df,
                how="left",
                left_on=ExecutionsSourceColumns.ORDER_ID + '_execution',
                right_on=OrdersSourceColumns.ORDER_ID,
                suffixes=('', '_orders')
            )
            logger_.info(f"Enriched consolidated data with Orders: {len(consolidated_df)} records")

        # Split into batches and upload
        if not consolidated_df.empty:
            # Add to a new file type for consolidated data
            consolidated_file_type = "executions_allocations_consolidated"
            if consolidated_file_type not in file_paths_by_type:
                file_paths_by_type[consolidated_file_type] = []

            # Generate output file path
            output_file_name_consolidated = Path(
                f"{output_file_name}_{consolidated_file_type}.csv"
            )
            output_file_path = (
                cloud_provider_prefix
                + realm
                + "/"
                + Path.joinpath(output_lake_path, output_file_name_consolidated).as_posix()
            )

            # Upload with batching
            self.upload_dataframe_with_batching(
                df=consolidated_df,
                output_file_path=output_file_path,
                batch_size=batch_size,
                file_paths_by_type=file_paths_by_type,
                file_type=consolidated_file_type,
            )

            logger_.info(f"Created consolidated Executions-Allocations files: {len(file_paths_by_type[consolidated_file_type])} batches")

    # TODO if this can be moved to a utils file as used by both here and aladdin
    @staticmethod
    def collect_required_file_uris(
            cloud_provider: CloudProviderEnum, file_url: str, realm: str
    ) -> pd.DataFrame:
        """Evaluate if the workflow meets all requirements to proceed. If it does
        not, raise an exception. If it does, return a DataFrame with the cloud URIs
        of each relevant file for this workflow's execution.

        :param cloud_provider: Cloud provider
        :param file_url: Absolute path of the input file
        :param realm: Tenant bucket without the cloud provider prefix

        :return: A DataFrame with the cloud URIs of each relevant file for this workflow's execution.
        """

        # Check if all files are present.
        # Raises an exception if the requirements are not met.
        # Returns a DataFrame with the file URLs if they are all present.
        multiple_files_input_controller_result: pd.DataFrame = run_multiple_files_input_controller(
            file_url=file_url,
            realm=realm,
            params=MultipleFilesInputControllerParams(
                list_of_files_regex=[
                    ".*(Executions|Allocations|Orders).*",
                ],
                # unique_identifier_regex=[
                #     UNIQUE_IDENTIFIER_REGEX,  # i.e "20220226 - YYYYMMDD"
                #     {
                #         "regex": ".*(Executions|Allocations|Orders)",
                #         "start_index": 0,
                #         "stop_index": 50,
                #     },  # Matched file type
                # ],
                file_links_in_output=True,
                prefix_for_file_links_in_output="",
            ),
            cloud_provider=cloud_provider,
            skip_serializer=True,
        )
        if multiple_files_input_controller_result.empty:
            raise SkipIfFileIsNotPartOfTheWorkflow(
                f"The input file: {Path(file_url).name} does not have to be processed."
            )
        return multiple_files_input_controller_result

    def batch_input_files(
        self,
        multiple_files_input_controller_result: pd.DataFrame,
        batch_size: int,
        realm: str,
        streamed: bool,
        audit_path: str,
        temp_dir: Path,
    ) -> Dict[str, List[FileSplitterResult]]:
        """This function is responsible for going through each relevant input file,
        and splitting them into reasonably sized batches.

        :param multiple_files_input_controller_result: DataFrame containing the cloud URIs of input files
        :param batch_size: Max batch size as defined in the config DB
        :param realm: Tenant bucket without the cloud provider prefix
        :param streamed: True or False, as defined in the config DB (for auditing purposes)
        :param audit_path: Absolute path of the JSON audit file.
        :param temp_dir: Temporary directory to store the batches in disk.

        :return: A dictionary where keys are file types and values are lists of FileSplitterResult objects.
        """

        logger_.info("Batching input files by type")

        batches_by_file_type: Dict[str, List[FileSplitterResult]] = {
            EzeSoftOMSControllerOutput.EXECUTIONS: [],
            EzeSoftOMSControllerOutput.ALLOCATIONS: [],
            EzeSoftOMSControllerOutput.ORDERS: [],
        }

        for _, row in multiple_files_input_controller_result.iterrows():
            file_uri = row["file_uri"]
            file_name = Path(file_uri).name

            # Determine file type based on filename
            file_type = None
            if "Executions" in file_name:
                file_type = EzeSoftOMSControllerOutput.EXECUTIONS
            elif "Allocations" in file_name:
                file_type = EzeSoftOMSControllerOutput.ALLOCATIONS
            elif "Orders" in file_name:
                file_type = EzeSoftOMSControllerOutput.ORDERS

            if file_type is None:
                logger_.warning(f"Could not determine file type for: {file_name}")
                continue

            logger_.info(f"Processing {file_type} file: {file_name}")

            # Download the file to temp directory
            local_file_path = temp_dir / file_name
            run_download_file(
                file_uri=file_uri,
                local_file_path=local_file_path.as_posix(),
            )

            # Split the file into batches
            file_splitter_results = run_csv_file_splitter(
                file_path=local_file_path.as_posix(),
                params=ParamsCsvFileSplitter(
                    max_chunk_size=batch_size,
                    output_dir=temp_dir.as_posix(),
                    detect_encoding=True,
                    normalise_columns=True,
                ),
            )

            batches_by_file_type[file_type].extend(file_splitter_results)

            # Update audit
            upsert_audit(
                audit_path=audit_path,
                streamed=streamed,
                workflow_status=[f"Processed {file_type} file: {file_name} into {len(file_splitter_results)} batches"],
            )

        return batches_by_file_type


def order_eze_soft_oms_controller_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    audit_path: str,
    **kwargs,
) -> Dict[str, Any]:
    class_instance = DefaultOrderEzeSoftOMSControllerCriteria()
    return class_instance.criteria_driver(
        aries_task_input=aries_task_input,
        file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
        workflow_config=workflow_config,
        audit_path=audit_path,
    )
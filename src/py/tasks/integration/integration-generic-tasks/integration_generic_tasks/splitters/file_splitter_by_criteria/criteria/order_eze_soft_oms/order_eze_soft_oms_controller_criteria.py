# ruff: noqa: E501
import fsspec
import json
import logging
import pandas as pd
import re
from addict import addict
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    Params as MultipleFilesInputControllerParams,
)
from aries_se_core_tasks.controllers.multiple_files_input_controller import (
    run_multiple_files_input_controller,
)
from aries_se_core_tasks.core.exception import TaskException
from aries_se_core_tasks.frame.merge_and_split_dataframes import (
    Params as MergeAndSplitDataFramesParams,
)
from aries_se_core_tasks.frame.merge_and_split_dataframes import (
    run_merge_and_split_dataframes,
)
from aries_se_core_tasks.io.read.cloud.download_file import (
    run_download_file,
)
from aries_se_core_tasks.io.read.csv_file_splitter import run_csv_file_splitter
from aries_task_link.models import AriesTaskInput
from collections import defaultdict
from datetime import datetime
from integration_audit.auditor import AuditorStaticFields, upsert_audit
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.abstract_order_eze_soft_oms_controller_criteria import (
    AbstractOrderEzeSoftOMSControllerCriteria,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.order_eze_soft_oms_criteria_exceptions import (
    SkipIfFileIsNotPartOfTheWorkflow,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.criteria.order_eze_soft_oms.order_eze_soft_oms_criteria_static import (
    UNIQUE_IDENTIFIER_REGEX,
    EzeSoftOMSControllerOutput,
    EzeSoftOMSFileTypeEnum,
    ExecutionsSourceColumns,
    AllocationsSourceColumns,
    OrdersSourceColumns,
    FileTypes,
    add_prefix,
    DevColumns,
)
from integration_generic_tasks.splitters.file_splitter_by_criteria.input_schema import (
    FileSplitterByCriteriaAriesTaskInput,
)
from pathlib import Path
from se_conductor_utils.task_output import DynamicTask, create_dynamic_tasks_list
from se_core_tasks.controllers.multiple_files_input_controller import (
    SkipIfMissingFiles,
    SkipIfSourceTimestampLessThanPair,
    SkipIfSourceTimestampSameAlphaLess,
)
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from se_core_tasks.core.exception import TaskException as CoreTaskException
from se_core_tasks.io.read.csv_file_splitter import (
    Params as ParamsCsvFileSplitter,
)
from se_core_tasks.io.read.csv_file_splitter import PreProcessFunc
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import (
    get_ingest_lake_dir_for_task,
    get_prefixed_ingest_lake_path_for_task,
)
from se_enums.cloud import CloudProviderEnum
from se_io_utils.tempfile_utils import tmp_directory
from typing import Any, Dict, List, Tuple

logger_ = logging.getLogger(__name__)


class DefaultOrderEzeSoftOMSControllerCriteria(AbstractOrderEzeSoftOMSControllerCriteria):
    @property
    def _should_process_executions_separately(self) -> bool:
        return False

    @property
    def _batch_size_override(self) -> int | None:
        return None

    def criteria_driver(
        self,
        aries_task_input: AriesTaskInput,
        file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
        workflow_config: addict.Dict,
        audit_path: str,
        **kwargs,
    ) -> Dict[str, Any]:
        """EZE Soft OMS controller criteria driver.

        This workflow processes files containing Executions, Allocations, and Orders data.
        The workflow is triggered by any of these file types and will check for the presence
        of all required files before proceeding.

        :param aries_task_input: Task input AriesTaskInput object propagated through Conductor
        :param file_splitter_by_criteria_aries_task_input: The params of `aries_task_input` converted to a standardized Pydantic object
        :param workflow_config: addict.Dict containing the order_eze_soft_oms workflow configuration fetched from the config DB.
        :param audit_path: The path to the JSON audit file, whose contents may be updated throughout this module.

        :return: A dictionary with keys for each file type containing lists of DynamicTask objects
        that will be executed downstream in the workflow.
        """

        cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
            file_uri=workflow_config.tenant.lake_prefix
        )
        realm: str = get_bucket(file_uri=file_splitter_by_criteria_aries_task_input.file_uri)
        streamed = workflow_config.workflow.streamed

        # Create temporary directory
        temp_dir: Path = tmp_directory()

        file_url: str = file_splitter_by_criteria_aries_task_input.file_uri


        multiple_files_input_controller_result: pd.DataFrame = run_multiple_files_input_controller(
            file_url=file_url,
            realm=realm,
            params=MultipleFilesInputControllerParams(
                list_of_files_regex=[
                    ".*(Executions|Allocations|Orders).*",
                ],
                # unique_identifier_regex=[
                #     UNIQUE_IDENTIFIER_REGEX,  # i.e "20220226 - YYYYMMDD"
                #     {
                #         "regex": ".*(Executions|Allocations|Orders)",
                #         "start_index": 0,
                #         "stop_index": 50,
                #     },  # Matched file type
                # ],
                file_links_in_output=True,
                prefix_for_file_links_in_output="",
            ),
            cloud_provider=cloud_provider,
            skip_serializer=True,
        )
        if multiple_files_input_controller_result.empty:
            raise SkipIfFileIsNotPartOfTheWorkflow(
                f"The input file: {Path(file_url).name} does not have to be processed."
            )


    def extract_and_process_files(
        self,
        file_url: str,
        realm: str,
        batch_size: int,
        temp_dir: Path,
        streamed: bool,
        cloud_provider: CloudProviderEnum,
        audit_path: str,
        aries_task_input: AriesTaskInput,
    ) -> Tuple[Dict[str, List[str]], str]:
        """This task is responsible for evaluating if the workflow meets all
        requirements and can proceed. If it can, it will iterate over the input
        files and process them into batches by file type.

        :param file_url: Absolute file path of the file that triggered the workflow
        :param realm: Tenant bucket without the cloud provider prefix
        :param batch_size: Max batch size as defined in the config DB
        :param temp_dir: Temporary directory where the batches are stored in disk
        :param streamed: True or false, as defined in the config DB.
        :param cloud_provider: Cloud provider as defined in the config DB.
        :param audit_path: Absolute path of the JSON audit file
        :param aries_task_input: Task input AriesTaskInput object propagated through Conductor

        :return: A tuple with exactly two elements:
        - A dictionary mapping file types to lists of cloud URIs for processing downstream
        - The cloud URI of the cache file that must be propagated downstream
        """

        logger_.info(f"Extracting and processing files for: {file_url}")

        file_paths_by_type: Dict[str, List[str]] = {
            EzeSoftOMSControllerOutput.EXECUTIONS: [],
            EzeSoftOMSControllerOutput.ALLOCATIONS: [],
            EzeSoftOMSControllerOutput.ORDERS: [],
        }
        cloud_provider_prefix = get_cloud_provider_prefix(value=cloud_provider)

        output_file_name = re.sub(
            r"\.(Executions|Allocations|Orders)\.",
            ".",
            Path(file_url).name,
        ).replace(".csv", "")

        # Create the appropriate path where the files are to be uploaded
        output_lake_path = Path(
            get_ingest_lake_dir_for_task(
                workflow_name=aries_task_input.workflow.name,
                task_name="file_splitter_by_criteria",
                workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
                workflow_trace_id=aries_task_input.workflow.trace_id,
            )
        )

        multiple_files_input_controller_df: pd.DataFrame = self.collect_required_file_uris(
            cloud_provider, file_url, realm
        )

        logger_.info("Input file requirements were met. Starting to process the input files.")

        # Process files by type and create batches
        batches_by_file_type: Dict[str, List[FileSplitterResult]] = self.batch_input_files(
            multiple_files_input_controller_result=multiple_files_input_controller_df,
            batch_size=batch_size,
            realm=realm,
            streamed=streamed,
            audit_path=audit_path,
            temp_dir=temp_dir,
        )


def order_eze_soft_oms_controller_criteria(
    aries_task_input: AriesTaskInput,
    file_splitter_by_criteria_aries_task_input: FileSplitterByCriteriaAriesTaskInput,
    workflow_config: addict.Dict,
    audit_path: str,
    **kwargs,
) -> Dict[str, Any]:
    class_instance = DefaultOrderEzeSoftOMSControllerCriteria()
    return class_instance.criteria_driver(
        aries_task_input=aries_task_input,
        file_splitter_by_criteria_aries_task_input=file_splitter_by_criteria_aries_task_input,
        workflow_config=workflow_config,
        audit_path=audit_path,
    )
import abc
from abc import ABC
from typing import Dict


class AbstractOrderEzeSoftOMSControllerCriteria(ABC):
    @property
    @abc.abstractmethod
    def _should_process_executions_separately(self) -> bool:
        """
        This property determines whether executions should be processed
        separately from orders and allocations. This can be enabled for
        specific tenant overrides that require different processing logic.

        :return: True if executions should be processed separately, False otherwise.
        """
        raise NotImplementedError

    @property
    @abc.abstractmethod
    def _batch_size_override(self) -> int | None:
        """
        This property allows tenant-specific overrides for batch size.
        If None is returned, the default batch size from workflow config will be used.

        :return: Custom batch size or None to use default.
        """
        raise NotImplementedError

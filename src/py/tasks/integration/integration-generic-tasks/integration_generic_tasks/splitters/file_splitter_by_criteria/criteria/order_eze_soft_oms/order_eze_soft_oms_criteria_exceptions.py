from aries_se_core_tasks.core.exception import TaskException


class SkipIfFileIsNotPartOfTheWorkflow(TaskException):
    """This exception is raised whenever the input file that triggers the
    "order_eze_soft_oms" workflow is an expected file. Meaning that the client
    and SteelEye are aware that this file is sent, however, the file does not
    need to be processed by the workflow.

    Hence, it essentially gets skipped.

    Unknown files that the client accidentally sends, or that SteelEye do not expect
    will raise a different exception that is not handled and that will crash the workflow.
    """

    pass

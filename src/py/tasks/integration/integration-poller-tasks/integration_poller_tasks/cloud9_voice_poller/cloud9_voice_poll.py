import base64
import hashlib
import hmac
import httpx
import io
import json
import logging
import requests
import time
import uuid
import zipfile
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from datetime import datetime, timedelta, timezone
from enum import StrEnum
from integration_poller_tasks.cloud9_voice_poller.static import (
    ATTACHMENT_DIRECTORY,
    BATCH_SIZE,
    CLOUD9_API_BASE_URL,
    CLOUD9_API_BASE_URL_NO_SCHEME,
    CLOUD9_API_PORT,
    METADATA_ENDPOINT,
    PAGE_SIZE,
    POLLER_NAME,
    RECORDINGS_ENDPOINT,
)
from se_comms_ingress_utils.abstractions.api_voice_poller import AbstractAPIVoicePoller
from se_comms_ingress_utils.common_util import (
    PollIntervalAndBackfill,
    get_start_end_date,
    has_date_range_in_event,
)
from se_data_lake.lake_path import get_non_streamed_poller_file_path
from se_fsspec_utils.file_utils import is_exist, write
from typing import Any, Dict

logger = logging.getLogger(POLLER_NAME)

ATTACHMENT_FAILURES = []  # type: ignore


class RequestMethods(StrEnum):
    GET = "GET"
    POST = "POST"


class Cloud9VoicePoll(AbstractAPIVoicePoller):
    def __init__(self, aries_task_input: AriesTaskInput, config):
        super().__init__(aries_task_input, config)
        self._user_name = aries_task_input.input_param.params.get("user_name", "default")
        self._attachment_file_names: list = []
        hours = aries_task_input.input_param.params.get("interval_duration_in_hours", 24)
        self.interval_param_exists = (
            True if aries_task_input.input_param.params.get("interval_duration_in_hours") else False
        )
        self.interval_duration_in_hours = timedelta(hours=int(hours))
        # default end date should be t-1
        # polling only till t-1 since there are periodic updates to metadata and
        # recording on cloud9 side which result in duplicates on our end also
        # adding to processing overhead
        self._default_end_date = datetime.now(timezone.utc) - timedelta(days=1)
        # default start time should depend on the backfill duration parameter
        # (default set same as previously used duration)
        self._default_start_date = self._default_end_date - self.interval_duration_in_hours

    def _upload_voice_recordings(self, response: requests.Response) -> None:
        """_upload_voice_recordings uploads the recording.

        :param response: API response object
        :type response: requests.Response
        """
        file_path = ""
        logger.info("Unzip API response content for voice recording files.")
        try:
            recordings_zip = io.BytesIO(response.content)
            with zipfile.ZipFile(recordings_zip) as zf:
                if len(zf.namelist()) == 0:
                    logger.info("No voice recordings found in API response")
                    return

                for recording_filename in zf.namelist():
                    content = zf.open(recording_filename).read()
                    file_path = f"{self._attachment_path_prefix.rstrip('/')}/{recording_filename}"
                    logger.info(f"Uploading to object store path: {file_path}")
                    write(
                        fs=self._destination_fs,
                        target_path=file_path,
                        file_content=content,
                    )
                    self._attachment_file_names.append(recording_filename)
                    logger.info(f"Uploaded recording to path {file_path}")
        except Exception as exc:
            logger.error(f"Failed to download attachment: `{exc}`")
            ATTACHMENT_FAILURES.append(f"Failed to download attachment for file: `{file_path}`")

    def _fetch_api_data(
        self, api_endpoint, next_page_token: str | None = None
    ) -> requests.Response:
        """Fetches recordings from the server."""
        body = {
            "beginDate": self._poll_info.from_date,
            "endDate": self._poll_info.to_date,
            "pageSize": PAGE_SIZE,
        }
        if next_page_token:
            body["nextPageToken"] = next_page_token

        headers = self._set_auth_headers(
            api_endpoint=api_endpoint, body=body, method=RequestMethods.POST
        )
        return self._requests_retriable_call(
            requests.post,
            url=f"{CLOUD9_API_BASE_URL}{api_endpoint}",
            json=body,
            headers=headers,
            timeout=300,
        )

    def _get_attachment(self) -> None:  # type: ignore
        """_get_attachment uploads the recordings."""
        response = self._fetch_api_data(api_endpoint=RECORDINGS_ENDPOINT)
        if response.status_code != httpx.codes.OK:
            raise ValueError(
                f"Unexpected response {response.status_code} from Cloud9 API Error: {response.text}"
            )
        logger.info("Downloaded page 1 of response.")
        self._upload_voice_recordings(response=response)

        next_page_token = response.headers.get("Next-Page-Token")

        while next_page_token:
            logger.info("Paginating for more data")
            response = self._fetch_api_data(
                api_endpoint=RECORDINGS_ENDPOINT, next_page_token=next_page_token
            )
            if response.status_code != httpx.codes.OK:
                raise ValueError(
                    f"Unexpected response {response.status_code} from Cloud9 API "
                    f"Error: {response.text}"
                )
            logger.info("Downloaded response.")
            self._upload_voice_recordings(response=response)
            next_page_token = response.headers.get("Next-Page-Token")

        logger.info("[DOWNLOAD COMPLETE] for all recordings")

    def _generate_hmac_signature_key(
        self,
        request_path: str,
        request_body: dict,
        request_timestamp: str,
        request_method: str,
        content_type: str,
    ) -> str:
        """Generates a HMAC SHA512 signature using Cloud9's public key and
        private secret.

        The API secret is used to seed the SHA512 hashing algorithm.
        The API key, along with the entire contents of the request (including headers and body)
        are combined into a string which is passed into the hashing algorithm.

        NOTE:
        IP address must be whitelisted from where requests will be sent.
        IPs are whitelisted as part of the API key generation process.
        (Please see SteelEye's Cloud9 representative for any issues relating to API access)
        """
        # get uuid as a 32-character hexadecimal string
        hex_id = uuid.uuid4().hex
        secret_key = self._secrets[self._user_name].secret_key
        api_key = self._secrets[self._user_name].api_key
        logger.info(f"Generating HMAC signature key for endpoint {request_path}")
        message = (
            f"{request_method}\n"
            f"https\n"
            f"{CLOUD9_API_BASE_URL_NO_SCHEME}:{CLOUD9_API_PORT}\n"
            f"{request_path}\n"
            f"{content_type}\n"
            f"{api_key}\n"
            f"{hex_id}\n"
            f"{request_timestamp}\n"
            f"{json.dumps(request_body)}\n"
        )
        signature = hmac.new(
            bytes(str(secret_key), encoding="utf-8"),
            bytes(message, encoding="utf-8"),
            hashlib.sha512,
        )
        return str(f"HmacSHA512 {api_key}:{hex_id}:{base64.b64encode(signature.digest()).decode()}")

    def _set_auth_headers(self, api_endpoint: str, body: dict, method: str) -> Dict[str, Any]:
        """_set_auth_headers returns auth headers.

        :param api_endpoint: api endpoint
        :type api_endpoint: str
        :param body: body params
        :type body: dict
        :param method: request method
        :type method: str
        :return: auth header dictionary
        :rtype: Dict[str, Any]
        """
        # prepare headers
        request_datetime = datetime.utcnow().strftime("%a, %d %b %Y %H:%M:%S GMT")
        hmac_signature = self._generate_hmac_signature_key(
            request_path=api_endpoint,
            request_body=body,
            request_timestamp=request_datetime,
            request_method=method,
            content_type="application/json",
        )
        return {
            "Content-Type": "application/json",
            "Authorization": hmac_signature,
            "Date": request_datetime,
        }

    def _is_recording_in_s3(self, recording_filename):
        recording_file_path = f"{self._attachment_path_prefix.rstrip('/')}/{recording_filename}"
        return is_exist(self._destination_fs, recording_file_path)

    def _download_recording(self, recording_filename) -> bool:
        body = {
            "beginDate": self._default_end_date.strftime("%Y-%m-%d %H:%M:%S"),  # start date = t-1
            "endDate": self._timestamp_now.strftime("%Y-%m-%d %H:%M:%S"),  # end date = now
            "pageSize": PAGE_SIZE,
            "filename": [recording_filename],
        }

        headers = self._set_auth_headers(
            api_endpoint=RECORDINGS_ENDPOINT, body=body, method=RequestMethods.POST
        )
        try:
            response = self._requests_retriable_call(
                requests.post,
                url=f"{CLOUD9_API_BASE_URL}{RECORDINGS_ENDPOINT}",
                json=body,
                headers=headers,
                timeout=300,
            )
        # for handling requests.exceptions.HTTPError: 422 Client Error: for url:
        # https://calldataapi.xhoot.com/v1/calls/recordings
        except Exception:
            logger.exception(f"[Download failed] for {recording_filename}")
            return False

        if response.status_code != httpx.codes.OK:
            return False

        logger.info("Downloaded response.")
        self._upload_voice_recordings(response=response)
        return True

    def _update_metadata(self, response):
        metadata = []
        for cdr in response["callRecords"]:
            # use voice recording file name as name for related CDR meta-data file
            recording_filename = cdr["recordingFile"]["recordingFileName"]
            # handle stringified None : "recordingFileName": "None"
            if recording_filename and recording_filename.lower() == "none":
                continue
            if (
                recording_filename in self._attachment_file_names
                or self._is_recording_in_s3(recording_filename)
                or self._download_recording(recording_filename)
            ):
                attachment_object_path = f"{ATTACHMENT_DIRECTORY.rstrip('/')}/{recording_filename}"
                cdr["steeleye_meta"] = {"attachment_object_path": attachment_object_path}
            metadata.append(cdr)

        return metadata

    def _get_metadata(self) -> None:  # type: ignore
        """Retrieve metadata for calls within a specified date range."""
        batched_metadata = []
        logger.info("Downloading metadata.")
        response = self._fetch_api_data(api_endpoint=METADATA_ENDPOINT)
        if response.status_code != httpx.codes.OK:
            raise ValueError(
                f"Unexpected response {response.status_code} from Cloud9 API Error: {response.text}"
            )
        response = response.json()
        logger.info("Downloaded page 1 of response.")
        batched_metadata.extend(self._update_metadata(response))

        next_page_token = response.get("nextPageToken")  # type: ignore
        while next_page_token:
            logger.info("Paginating for more data")
            time.sleep(4)
            response = self._fetch_api_data(
                api_endpoint=METADATA_ENDPOINT, next_page_token=next_page_token
            )
            if response.status_code != httpx.codes.OK:
                raise ValueError(
                    f"Unexpected response {response.status_code} from Cloud9 API "
                    f"Error: {response.text}"
                )
            response = response.json()
            batched_metadata.extend(self._update_metadata(response))
            logger.info("Downloaded response.")
            next_page_token = response.get("nextPageToken")  # type: ignore

        logger.info(f"[DOWNLOAD COMPLETE] for metadata :{len(batched_metadata)}")

        logger.info(f"Uploading batched metadata to path {self._path_prefix}")
        self._upload_batched_metadata(batched_metadata, BATCH_SIZE, self._path_prefix)
        logger.info(f"Uploaded batched metadata to path {self._path_prefix}")

    def _get_poll_interval_and_backfill(
        self, aries_task_input, workflow_last_executed
    ) -> PollIntervalAndBackfill:
        if has_date_range_in_event(aries_task_input):
            from_date, to_date = get_start_end_date(
                start_date=aries_task_input.input_param.params["from_date"],
                end_date=aries_task_input.input_param.params["to_date"],
            )
            backfill = True
            to_date = to_date.replace(tzinfo=timezone.utc)
            # in case of back-fill, add validation to  make sure back-fill is only till t-1
            if to_date > self._default_end_date:
                raise Exception("end date cannot exceed yesterday's date")
        else:
            if workflow_last_executed and self.interval_param_exists:
                # For cases where the workflow last executed is not updated
                # for a long time because of failed workflow runs,
                # we may want to set the start date != workflow_last_executed,
                # and then update the workflow_last_executed
                # for the poller to run as expected for the upcoming executions.
                # The missing data can be backfilled using from_date and to_date manually.

                # To use this, we check if the interval_param_exists param is True.
                # The _default_start_date already has the interval subtracted from
                # the _default_end_date
                from_date = self._default_start_date

            elif workflow_last_executed:
                # get the last execution time from the db
                from_date = datetime.strptime(workflow_last_executed, "%Y-%m-%dT%H:%M:%S")
            else:
                from_date = self._default_start_date

            to_date = self._default_end_date
            backfill = False
        return PollIntervalAndBackfill(from_date=from_date, to_date=to_date, backfill=backfill)

    def run_poller(
        self,
    ) -> AriesTaskResult:
        self._poll_info = self._get_poll_interval_and_backfill(
            self._aries_task_input,
            self._poller_tenant_workflow_config.workflow_execution_ref[self._user_name]
            if self._poller_tenant_workflow_config.workflow_execution_ref
            else None,
        )
        logging.info(
            f"Backfill: {self._poll_info.backfill}, "
            f"Poll from date {self._poll_info.from_date}, "
            f"Poll to date: {self._poll_info.to_date}"
        )

        self._poll_info.from_date = self._poll_info.from_date.strftime("%Y-%m-%d %H:%M:%S")  # type:ignore
        self._poll_info.to_date = self._poll_info.to_date.strftime("%Y-%m-%d %H:%M:%S")  # type:ignore

        logger.info("[DOWNLOADING] data from Cloud9 Voice API.")
        path = get_non_streamed_poller_file_path(
            workflow_name=self._workflow_name,
            workflow_start_timestamp=self._timestamp_now,
            workflow_trace_id=self._aries_task_input.workflow.trace_id,
            custom_path=self._custom_lake_path,
            is_evented=True,
        )
        self._path_prefix = (
            f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}/{path}"
        )
        self._attachment_path_prefix = (
            f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}"
            f"/{ATTACHMENT_DIRECTORY}"
        )
        self._get_attachment()
        self._get_metadata()
        logger.info("Completed Downloading data from Cloud9 Voice API.")

        if not self._poll_info.backfill:
            # set last execution time in db
            logger.info("Setting last execution time in database")
            # poller should always store last polled time to t-1
            CompatibleTenantWorkflowAPIClient.update(
                tenant_workflow_api=self._tenant_workflow_api,
                json_body={
                    "workflow_execution_ref": {  # type: ignore
                        self._user_name: self._default_end_date.strftime("%Y-%m-%dT%H:%M:%S")
                    }
                },
                tenant_name=self._tenant_name,
                workflow_name=self._workflow_name,
            )

        if ATTACHMENT_FAILURES:
            logger.error(f"Failed to download attachment: {ATTACHMENT_FAILURES}")
            raise Exception("Some of the attachments were not downloaded")

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)
